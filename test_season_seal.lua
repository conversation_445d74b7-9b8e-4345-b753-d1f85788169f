-- 季节符印功能测试脚本
-- 验证季匠可以主动切换季节刻印

local function TestSeasonSeal()
    print("=== 季节符印功能测试 ===")
    
    local player = ThePlayer or AllPlayers[1]
    if not player then
        print("❌ 未找到玩家")
        return
    end
    
    -- 1. 检查玩家是否有季节刻印组件
    print("\n--- 检查季节刻印组件 ---")
    if not player.components.season_engraving then
        print("❌ 玩家没有季节刻印组件")
        print("提示：请使用季匠角色进行测试")
        return
    else
        print("✓ 玩家具有季节刻印组件")
    end
    
    -- 显示当前季节状态
    local current_season = player.components.season_engraving:GetSeason()
    local world_season = TheWorld.state.season
    local is_manual = player.components.season_engraving:IsManualSeason()
    
    print("当前刻印季节: " .. (current_season or "未知"))
    print("世界季节: " .. (world_season or "未知"))
    print("是否手动设置: " .. (is_manual and "是" or "否"))
    
    -- 2. 测试生成季节符印
    print("\n--- 测试季节符印生成 ---")
    local seasons = {"spring", "summer", "autumn", "winter"}
    local season_names = {
        spring = "春季",
        summer = "夏季",
        autumn = "秋季", 
        winter = "冬季"
    }
    
    local seals = {}
    for _, season in ipairs(seasons) do
        local seal = SpawnPrefab("season_seal_" .. season)
        if seal then
            player.components.inventory:GiveItem(seal)
            table.insert(seals, seal)
            print("✓ " .. season_names[season] .. "符印生成成功")
        else
            print("❌ " .. season_names[season] .. "符印生成失败")
        end
    end
    
    -- 3. 测试季节切换功能
    print("\n--- 测试季节切换功能 ---")
    
    for i, season in ipairs(seasons) do
        local seal = seals[i]
        if seal and seal.components and seal.components.useableitem then
            print("测试切换到" .. season_names[season] .. "...")
            
            -- 记录切换前的状态
            local old_season = player.components.season_engraving:GetSeason()
            
            -- 使用符印
            local success = seal.components.useableitem:OnUsed(player)
            
            if success then
                -- 检查切换后的状态
                local new_season = player.components.season_engraving:GetSeason()
                local is_manual_now = player.components.season_engraving:IsManualSeason()
                
                if new_season == season and is_manual_now then
                    print("✓ 成功切换到" .. season_names[season])
                    print("  - 刻印季节: " .. new_season)
                    print("  - 手动设置: " .. (is_manual_now and "是" or "否"))
                else
                    print("❌ 切换失败")
                    print("  - 期望季节: " .. season)
                    print("  - 实际季节: " .. (new_season or "未知"))
                    print("  - 手动设置: " .. (is_manual_now and "是" or "否"))
                end
            else
                print("❌ 符印使用失败")
            end
            
            -- 短暂延迟，让特效播放
            player:DoTaskInTime(0.5, function() end)
        else
            print("❌ " .. season_names[season] .. "符印无效或缺少使用组件")
        end
    end
    
    -- 4. 测试清除手动设置
    print("\n--- 测试清除手动设置 ---")
    
    local old_season = player.components.season_engraving:GetSeason()
    local old_manual = player.components.season_engraving:IsManualSeason()
    
    print("清除前: 季节=" .. (old_season or "未知") .. ", 手动=" .. (old_manual and "是" or "否"))
    
    -- 清除手动设置
    player.components.season_engraving:ClearManualSeason()
    
    local new_season = player.components.season_engraving:GetSeason()
    local new_manual = player.components.season_engraving:IsManualSeason()
    local world_season = TheWorld.state.season
    
    print("清除后: 季节=" .. (new_season or "未知") .. ", 手动=" .. (new_manual and "是" or "否"))
    print("世界季节: " .. (world_season or "未知"))
    
    if new_season == world_season and not new_manual then
        print("✓ 成功回到跟随世界季节")
    else
        print("❌ 清除手动设置失败")
    end
    
    -- 5. 测试季节效果应用
    print("\n--- 测试季节效果应用 ---")
    
    -- 切换到春季测试防水效果
    if seals[1] and seals[1].components and seals[1].components.useableitem then
        seals[1].components.useableitem:OnUsed(player)
        
        -- 检查是否有防水组件
        if player.components.waterproofer then
            local effectiveness = player.components.waterproofer:GetEffectiveness()
            print("✓ 春季防水效果: " .. (effectiveness * 100) .. "%")
        else
            print("❌ 春季防水效果未应用")
        end
    end
    
    -- 6. 测试与武器的配合
    print("\n--- 测试与季节之刃配合 ---")
    
    -- 生成季节之刃
    local blade = SpawnPrefab("season_blade")
    if blade then
        player.components.inventory:GiveItem(blade)
        
        -- 装备武器
        player.components.inventory:Equip(blade)
        
        -- 检查武器的季节检测
        if blade.components and blade.components.inspectable then
            local status = blade.components.inspectable:GetStatus(blade)
            print("武器状态: " .. (status or "无状态"))
        end
        
        print("✓ 季节之刃与符印配合测试完成")
    else
        print("❌ 无法生成季节之刃")
    end
    
    print("\n=== 季节符印测试完成 ===")
    print("功能说明：")
    print("- 季节符印允许季匠主动切换季节刻印")
    print("- 手动设置的季节优先于世界季节")
    print("- 可以清除手动设置，回到跟随世界季节")
    print("- 季节切换会立即应用对应的被动效果")
    print("- 影响季节之刃的季节效果和Boss破盾机制")
    print("\n制作配方：纸莎草x1 + 对应季节碎片x2")
end

return TestSeasonSeal
