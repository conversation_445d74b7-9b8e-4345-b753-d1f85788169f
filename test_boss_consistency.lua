-- 季冠树守Boss一致性测试脚本
-- 验证Boss实现是否与设计文档一致

local function TestBossConsistency()
    print("=== 季冠树守Boss一致性测试 ===")
    
    local player = ThePlayer or AllPlayers[1]
    if not player then
        print("❌ 未找到玩家")
        return
    end
    
    -- 1. 测试Boss基础数值
    print("\n--- 测试Boss基础数值 ---")
    local boss = SpawnPrefab("boss_season_warden")
    if not boss then
        print("❌ Boss生成失败")
        return
    end
    
    boss.Transform:SetPosition(player.Transform:GetWorldPosition())
    
    -- 检查生命值
    local expected_hp = TUNING.SEASON_BOSS_HP or 8000
    local actual_hp = boss.components.health:GetMaxHealth()
    if actual_hp == expected_hp then
        print("✓ 生命值正确: " .. actual_hp)
    else
        print("❌ 生命值不匹配: 期望" .. expected_hp .. ", 实际" .. actual_hp)
    end
    
    -- 检查伤害
    local expected_damage = 75
    local actual_damage = boss.components.combat.defaultdamage
    if actual_damage == expected_damage then
        print("✓ 伤害值正确: " .. actual_damage)
    else
        print("❌ 伤害值不匹配: 期望" .. expected_damage .. ", 实际" .. actual_damage)
    end
    
    -- 检查护盾减免
    local expected_absorb = TUNING.SEASON_BOSS_SHIELD_ABSORB or 0.2
    if expected_absorb == 0.2 then
        print("✓ 护盾减免正确: " .. (expected_absorb * 100) .. "%")
    else
        print("❌ 护盾减免不匹配: 期望20%, 实际" .. (expected_absorb * 100) .. "%")
    end
    
    -- 2. 测试四季阶段机制
    print("\n--- 测试四季阶段机制 ---")
    local phases = {"spring", "summer", "autumn", "winter"}
    local phase_names = {
        spring = "春季",
        summer = "夏季", 
        autumn = "秋季",
        winter = "冬季"
    }
    
    for i, phase in ipairs(phases) do
        -- 模拟血量损失触发阶段切换
        local target_percent = 1 - (i * 0.25)
        boss.components.health:SetPercent(target_percent + 0.01)  -- 稍微高一点避免直接触发下一阶段
        
        -- 手动触发阶段切换
        boss._phase = phase
        boss._shielded = true
        
        print("✓ " .. phase_names[phase] .. "阶段设置完成")
        
        -- 测试护盾状态
        if boss._shielded then
            print("  - 护盾状态: 开启")
        else
            print("  - 护盾状态: 关闭")
        end
    end
    
    -- 3. 测试破盾机制
    print("\n--- 测试破盾机制 ---")
    
    -- 测试季芯炸符破盾
    boss._phase = "spring"
    boss._shielded = true
    
    -- 模拟春季炸符破盾
    boss:PushEvent("season_sigil", {season = "spring"})
    
    if not boss._shielded then
        print("✓ 季芯炸符破盾成功")
    else
        print("❌ 季芯炸符破盾失败")
    end
    
    -- 重置护盾测试武器破盾
    boss._shielded = true
    boss._weapon_hits = 0
    
    -- 模拟武器连击破盾
    for i = 1, 3 do
        boss:PushEvent("season_sigil", {source = "weapon_burst"})
    end
    
    if not boss._shielded then
        print("✓ 武器连击破盾成功")
    else
        print("❌ 武器连击破盾失败")
    end
    
    -- 4. 测试季节技能
    print("\n--- 测试季节技能 ---")
    
    -- 为每个阶段测试技能
    for _, phase in ipairs(phases) do
        boss._phase = phase
        print("测试" .. phase_names[phase] .. "技能...")
        
        -- 调用技能函数（这里只是验证不会报错）
        local success, err = pcall(function()
            -- 这里应该调用DoPhaseSkill，但由于它是local函数，我们只能间接测试
            boss:PushEvent("attacked", {attacker = player, damage = 10})
        end)
        
        if success then
            print("✓ " .. phase_names[phase] .. "技能执行无错误")
        else
            print("❌ " .. phase_names[phase] .. "技能执行出错: " .. tostring(err))
        end
    end
    
    -- 5. 测试掉落物
    print("\n--- 测试掉落物 ---")
    
    if boss.components.lootdropper and boss.components.lootdropper.droplootfn then
        local loots = boss.components.lootdropper.droplootfn(boss)
        
        -- 检查固定掉落的季芯
        local season_core_count = 0
        for _, loot in ipairs(loots) do
            if loot == "season_core" then
                season_core_count = season_core_count + 1
            end
        end
        
        if season_core_count >= 2 then
            print("✓ 季芯掉落正确: " .. season_core_count .. "个")
        else
            print("❌ 季芯掉落不足: 期望至少2个, 实际" .. season_core_count .. "个")
        end
        
        print("完整掉落列表:")
        for i, loot in ipairs(loots) do
            print("  " .. i .. ". " .. loot)
        end
    else
        print("❌ 掉落函数未找到")
    end
    
    -- 6. 测试TUNING配置一致性
    print("\n--- 测试TUNING配置一致性 ---")
    
    local tuning_checks = {
        {name = "武器破盾时间窗口", key = "SEASON_BOSS_WEAPON_BREAK_WINDOW", expected = 8},
        {name = "护盾吸收率", key = "SEASON_BOSS_SHIELD_ABSORB", expected = 0.2},
        {name = "Boss生命值", key = "SEASON_BOSS_HP", expected = 8000},
    }
    
    for _, check in ipairs(tuning_checks) do
        local actual = TUNING[check.key]
        if actual == check.expected then
            print("✓ " .. check.name .. "正确: " .. tostring(actual))
        else
            print("❌ " .. check.name .. "不匹配: 期望" .. tostring(check.expected) .. ", 实际" .. tostring(actual))
        end
    end
    
    -- 清理测试Boss
    if boss and boss:IsValid() then
        boss:Remove()
    end
    
    print("\n=== Boss一致性测试完成 ===")
    print("说明：")
    print("- Boss基础数值应与设计文档一致")
    print("- 四季阶段机制应正确切换")
    print("- 破盾机制应支持炸符和武器两种方式")
    print("- 季节技能应对应四个不同阶段")
    print("- 掉落物应包含固定的季芯x2")
    print("- TUNING配置应与设计文档数值匹配")
end

return TestBossConsistency
