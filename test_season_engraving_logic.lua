-- 季节刻印逻辑测试脚本
-- 验证新的季节刻印逻辑：世界季节变化时重置，同一季节内保持手动设置

local function TestSeasonEngravingLogic()
    print("=== 季节刻印逻辑测试 ===")
    
    local player = ThePlayer or AllPlayers[1]
    if not player then
        print("❌ 未找到玩家")
        return
    end
    
    -- 检查是否为季匠角色
    if not player.components.season_engraving then
        print("❌ 当前角色不是季匠")
        print("提示：请使用季匠角色进行测试")
        return
    end
    
    print("✓ 检测到季匠角色")
    
    -- 1. 显示当前状态
    print("\n--- 当前状态 ---")
    local current_season = player.components.season_engraving:GetSeason()
    local world_season = TheWorld.state.season
    local is_manual = player.components.season_engraving:IsManualSeason()
    
    print("世界季节: " .. (world_season or "未知"))
    print("刻印季节: " .. (current_season or "未知"))
    print("手动设置: " .. (is_manual and "是" or "否"))
    
    -- 2. 测试手动设置季节
    print("\n--- 测试手动设置季节 ---")
    
    -- 生成春季符印
    local spring_seal = SpawnPrefab("season_seal_spring")
    if spring_seal then
        player.components.inventory:GiveItem(spring_seal)
        print("✓ 获得春季符印")
        
        -- 使用符印切换到春季
        print("使用春季符印...")
        local success = spring_seal.components.useableitem:OnUsed(player)
        
        if success then
            local new_season = player.components.season_engraving:GetSeason()
            local new_manual = player.components.season_engraving:IsManualSeason()
            
            print("✓ 切换成功")
            print("  新刻印季节: " .. (new_season or "未知"))
            print("  手动设置: " .. (new_manual and "是" or "否"))
            
            if new_season == "spring" and new_manual then
                print("✓ 手动设置春季成功")
            else
                print("❌ 手动设置失败")
            end
        else
            print("❌ 符印使用失败")
        end
    else
        print("❌ 无法生成春季符印")
        return
    end
    
    -- 3. 测试同一世界季节内保持手动设置
    print("\n--- 测试同一世界季节内保持手动设置 ---")
    
    -- 生成夏季符印
    local summer_seal = SpawnPrefab("season_seal_summer")
    if summer_seal then
        player.components.inventory:GiveItem(summer_seal)
        
        -- 切换到夏季
        print("在同一世界季节内切换到夏季...")
        local success = summer_seal.components.useableitem:OnUsed(player)
        
        if success then
            local new_season = player.components.season_engraving:GetSeason()
            local new_manual = player.components.season_engraving:IsManualSeason()
            local world_season_now = TheWorld.state.season
            
            print("✓ 切换成功")
            print("  世界季节: " .. (world_season_now or "未知"))
            print("  刻印季节: " .. (new_season or "未知"))
            print("  手动设置: " .. (new_manual and "是" or "否"))
            
            if new_season == "summer" and new_manual then
                print("✓ 同一世界季节内手动设置保持有效")
            else
                print("❌ 手动设置未保持")
            end
        else
            print("❌ 夏季符印使用失败")
        end
    else
        print("❌ 无法生成夏季符印")
    end
    
    -- 4. 模拟世界季节变化
    print("\n--- 模拟世界季节变化 ---")
    
    local old_world_season = TheWorld.state.season
    local old_engraving_season = player.components.season_engraving:GetSeason()
    local old_manual = player.components.season_engraving:IsManualSeason()
    
    print("变化前:")
    print("  世界季节: " .. (old_world_season or "未知"))
    print("  刻印季节: " .. (old_engraving_season or "未知"))
    print("  手动设置: " .. (old_manual and "是" or "否"))
    
    -- 模拟季节变化（这里我们手动触发季节变化事件）
    local new_world_season = "winter"  -- 假设变化到冬季
    
    -- 手动设置世界季节（仅用于测试）
    TheWorld.state.season = new_world_season
    
    -- 触发季节变化事件
    player.components.season_engraving:OnWorldSeasonChanged()
    
    local new_engraving_season = player.components.season_engraving:GetSeason()
    local new_manual = player.components.season_engraving:IsManualSeason()
    
    print("变化后:")
    print("  世界季节: " .. (TheWorld.state.season or "未知"))
    print("  刻印季节: " .. (new_engraving_season or "未知"))
    print("  手动设置: " .. (new_manual and "是" or "否"))
    
    -- 验证逻辑
    if new_engraving_season == new_world_season and not new_manual then
        print("✓ 世界季节变化时正确重置手动设置")
    else
        print("❌ 世界季节变化处理有误")
    end
    
    -- 恢复原来的世界季节
    TheWorld.state.season = old_world_season
    
    -- 5. 测试角色检查功能
    print("\n--- 测试角色检查功能 ---")
    
    if player.components.inspectable then
        local status = player.components.inspectable:GetStatus(player)
        print("角色检查结果:")
        print(status or "无状态信息")
    else
        print("❌ 角色没有检查组件")
    end
    
    -- 6. 测试视觉效果
    print("\n--- 测试视觉效果 ---")
    
    -- 切换到不同季节测试颜色
    local seasons = {"spring", "summer", "autumn", "winter"}
    local season_names = {
        spring = "春季",
        summer = "夏季",
        autumn = "秋季",
        winter = "冬季"
    }
    
    for _, season in ipairs(seasons) do
        local seal = SpawnPrefab("season_seal_" .. season)
        if seal then
            player.components.inventory:GiveItem(seal)
            seal.components.useableitem:OnUsed(player)
            
            print("切换到" .. season_names[season] .. "，观察角色颜色变化")
            
            -- 短暂延迟让视觉效果显示
            player:DoTaskInTime(0.5, function() end)
        end
    end
    
    print("\n=== 测试完成 ===")
    print("新逻辑总结：")
    print("1. 世界季节变化时，手动设置会被清除")
    print("2. 同一世界季节内，手动设置保持有效")
    print("3. 可以通过检查角色查看当前季节状态")
    print("4. 角色有轻微的季节颜色提示")
    print("5. 手动设置的季节颜色更明显")
end

return TestSeasonEngravingLogic
