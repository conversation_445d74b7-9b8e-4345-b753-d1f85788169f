-- 季冠树守Boss设计一致性验证脚本
-- 验证代码实现是否完全符合设计文档

local function TestBossDesignConsistency()
    print("=== 季冠树守Boss设计一致性验证 ===")
    
    local player = ThePlayer or AllPlayers[1]
    if not player then
        print("❌ 未找到玩家")
        return
    end
    
    -- 1. 验证基础数值
    print("\n--- 验证基础数值 ---")
    local boss = SpawnPrefab("boss_season_warden")
    if not boss then
        print("❌ Boss生成失败")
        return
    end
    
    boss.Transform:SetPosition(player.Transform:GetWorldPosition())
    
    -- 生命值：8000
    local hp = boss.components.health:GetMaxHealth()
    print("生命值: " .. hp .. " (期望: 8000) " .. (hp == 8000 and "✓" or "❌"))
    
    -- 伤害：普通拍击 75
    local damage = boss.components.combat.defaultdamage
    print("普通攻击伤害: " .. damage .. " (期望: 75) " .. (damage == 75 and "✓" or "❌"))
    
    -- 护甲：0.8（80%减免）
    local shield_absorb = TUNING.SEASON_BOSS_SHIELD_ABSORB
    print("护盾减免: " .. (shield_absorb * 100) .. "% (期望: 80%) " .. (shield_absorb == 0.8 and "✓" or "❌"))
    
    -- 2. 验证四季护冠机制
    print("\n--- 验证四季护冠机制 ---")
    
    -- 初始状态应该有护盾
    print("初始护盾状态: " .. (boss._shielded and "开启" or "关闭") .. " " .. (boss._shielded and "✓" or "❌"))
    
    -- 验证异常免疫标签
    local has_immune_tag = boss:HasTag("season_shield_immune")
    print("异常免疫标签: " .. (has_immune_tag and "存在" or "不存在") .. " " .. (has_immune_tag and "✓" or "❌"))
    
    -- 测试阶段切换（每损失25%生命进入下一阶段）
    local phases = {"spring", "summer", "autumn", "winter"}
    for i, expected_phase in ipairs(phases) do
        local target_hp_percent = 1 - (i * 0.25) + 0.01  -- 稍微高一点避免跳过
        boss.components.health:SetPercent(target_hp_percent)
        
        -- 手动触发健康变化事件
        boss:PushEvent("healthdelta", {})
        
        local actual_phase = boss._phase
        print("阶段 " .. i .. " (血量" .. math.floor(target_hp_percent * 100) .. "%): " .. 
              (actual_phase or "nil") .. " (期望: " .. expected_phase .. ") " .. 
              (actual_phase == expected_phase and "✓" or "❌"))
    end
    
    -- 3. 验证破盾机制
    print("\n--- 验证破盾机制 ---")
    
    -- 重置为春季阶段进行测试
    boss._phase = "spring"
    boss._shielded = true
    boss:AddTag("season_shield_immune")
    
    -- 测试季芯炸符破盾
    print("测试春季炸符破盾...")
    boss:PushEvent("season_sigil", {season = "spring"})
    local shield_broken_by_sigil = not boss._shielded
    print("炸符破盾结果: " .. (shield_broken_by_sigil and "成功" or "失败") .. " " .. (shield_broken_by_sigil and "✓" or "❌"))
    
    -- 重置护盾测试武器破盾
    boss._shielded = true
    boss:AddTag("season_shield_immune")
    boss._weapon_hits = 0
    
    -- 给玩家添加春季刻印（模拟季节匹配）
    if not player.components.season_engraving then
        player:AddComponent("season_engraving")
    end
    player.components.season_engraving:SetSeason("spring")
    
    print("测试武器连击破盾（需要季节匹配）...")
    -- 模拟3次武器爆发
    for i = 1, 3 do
        boss:PushEvent("season_sigil", {source = "weapon_burst"})
    end
    local shield_broken_by_weapon = not boss._shielded
    print("武器破盾结果: " .. (shield_broken_by_weapon and "成功" or "失败") .. " " .. (shield_broken_by_weapon and "✓" or "❌"))
    
    -- 4. 验证季节技能伤害范围
    print("\n--- 验证季节技能伤害 ---")
    
    -- 检查各季节技能的伤害是否在30~50范围内
    local season_damages = {
        spring = 35,  -- 雷鸣号令
        summer = 40,  -- 炎热涌动
        autumn = 35,  -- 萧瑟旋风（落叶旋风）
        winter = 30   -- 寒潮吐息
    }
    
    for season, damage in pairs(season_damages) do
        local in_range = damage >= 30 and damage <= 50
        print(season .. "季技能伤害: " .. damage .. " (期望: 30~50) " .. (in_range and "✓" or "❌"))
    end
    
    -- 5. 验证异常免疫效果
    print("\n--- 验证异常免疫效果 ---")
    
    -- 重置护盾状态
    boss._shielded = true
    boss:AddTag("season_shield_immune")
    
    -- 添加seasonal_debuff组件进行测试
    if not boss.components.seasonal_debuff then
        boss:AddComponent("seasonal_debuff")
    end
    
    -- 测试护盾状态下的减速抗性
    print("测试护盾状态下的减速抗性...")
    boss.components.seasonal_debuff:ApplySlow(0.8, 4)  -- 20%减速，4秒
    local slow_mult = boss.components.seasonal_debuff:GetSlowMultiplier()
    local has_resistance = slow_mult > 0.8  -- 应该被减弱
    print("减速抗性: " .. (has_resistance and "生效" or "无效") .. " (减速倍率: " .. slow_mult .. ") " .. (has_resistance and "✓" or "❌"))
    
    -- 测试护盾状态下的灼伤抗性
    print("测试护盾状态下的灼伤抗性...")
    boss.components.seasonal_debuff:ApplyBurn(4, 4)  -- 4点/秒，4秒
    local burn_damage = boss.components.seasonal_debuff:GetBurnDamage()
    local burn_resistance = burn_damage < 4  -- 应该被减弱
    print("灼伤抗性: " .. (burn_resistance and "生效" or "无效") .. " (伤害: " .. burn_damage .. "/秒) " .. (burn_resistance and "✓" or "❌"))
    
    -- 6. 验证破盾后的季伤弱点
    print("\n--- 验证季伤弱点机制 ---")
    
    -- 破盾状态
    boss._shielded = false
    boss:RemoveTag("season_shield_immune")
    
    -- 模拟夏季攻击（应该触发增强灼伤）
    player.components.season_engraving:SetSeason("summer")
    local old_burn = boss.components.seasonal_debuff:GetBurnDamage()
    boss:PushEvent("attacked", {attacker = player, damage = 10})
    local new_burn = boss.components.seasonal_debuff:GetBurnDamage()
    local weakness_triggered = new_burn > old_burn
    print("季伤弱点触发: " .. (weakness_triggered and "成功" or "失败") .. " " .. (weakness_triggered and "✓" or "❌"))
    
    -- 7. 验证武器破盾时间窗口
    print("\n--- 验证武器破盾时间窗口 ---")
    local window = TUNING.SEASON_BOSS_WEAPON_BREAK_WINDOW
    print("武器破盾时间窗口: " .. window .. "秒 (期望: 8秒) " .. (window == 8 and "✓" or "❌"))
    
    -- 8. 验证掉落物
    print("\n--- 验证掉落物 ---")
    if boss.components.lootdropper and boss.components.lootdropper.droplootfn then
        local loots = boss.components.lootdropper.droplootfn(boss)
        local season_core_count = 0
        local has_seasonal_food = false
        
        for _, loot in ipairs(loots) do
            if loot == "season_core" then
                season_core_count = season_core_count + 1
            elseif loot:find("_cooked") then
                has_seasonal_food = true
            end
        end
        
        print("季芯掉落: " .. season_core_count .. "个 (期望: 2个) " .. (season_core_count == 2 and "✓" or "❌"))
        print("当季成品食物: " .. (has_seasonal_food and "存在" or "不存在") .. " " .. (has_seasonal_food and "✓" or "❌"))
    end
    
    -- 清理
    if boss and boss:IsValid() then
        boss:Remove()
    end
    
    print("\n=== 验证完成 ===")
    print("如果所有项目都显示 ✓，则代码实现与设计文档完全一致")
end

return TestBossDesignConsistency
